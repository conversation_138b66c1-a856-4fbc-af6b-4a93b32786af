<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class ApplicantJobsController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
    }

    public function index()
    {
        // Using dummy data for UI development
        $jobData = [
            [
                'exercise' => [
                    'id' => 1,
                    'title' => 'IT Recruitment Exercise 2024',
                    'description' => 'Annual recruitment for IT positions',
                    'status' => 'publish',
                    'org_id' => 1,
                    'deadline' => '2024-03-15',
                    'gazzetted_no' => 'GAZ2024001',
                    'advertisement_no' => 'ADV2024001',
                    'publish_date_from' => '2024-01-15',
                    'publish_date_to' => '2024-03-15'
                ],
                'organization' => [
                    'id' => 1,
                    'name' => 'Department of ICT',
                    'address' => 'Port Moresby, NCD',
                    'orglogo' => ''
                ],
                'position_count' => 5
            ],
            [
                'exercise' => [
                    'id' => 2,
                    'title' => 'Health Department Recruitment 2024',
                    'description' => 'Recruitment for health professionals',
                    'status' => 'publish',
                    'org_id' => 2,
                    'deadline' => '2024-03-20',
                    'gazzetted_no' => 'GAZ2024002',
                    'advertisement_no' => 'ADV2024002',
                    'publish_date_from' => '2024-01-20',
                    'publish_date_to' => '2024-03-20'
                ],
                'organization' => [
                    'id' => 2,
                    'name' => 'Department of Health',
                    'address' => 'Port Moresby, NCD',
                    'orglogo' => ''
                ],
                'position_count' => 8
            ],
            [
                'exercise' => [
                    'id' => 3,
                    'title' => 'Education Department Recruitment 2024',
                    'description' => 'Recruitment for teaching positions',
                    'status' => 'publish',
                    'org_id' => 3,
                    'deadline' => '2024-04-01',
                    'gazzetted_no' => 'GAZ2024003',
                    'advertisement_no' => 'ADV2024003',
                    'publish_date_from' => '2024-02-01',
                    'publish_date_to' => '2024-04-01'
                ],
                'organization' => [
                    'id' => 3,
                    'name' => 'Department of Education',
                    'address' => 'Port Moresby, NCD',
                    'orglogo' => ''
                ],
                'position_count' => 12
            ]
        ];

        return view('applicant/applicant_job_openings', [
            'title' => 'Job Openings',
            'menu' => 'jobs',
            'jobData' => $jobData
        ]);
    }

    public function view($exerciseId)
    {
        // Fetch exercise details
        $exercise = $this->exercisesModel
            ->where('id', $exerciseId)
            ->where('status', 'publish')
            ->first();

        if (!$exercise) {
            return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
        }

        // Get organization details
        $organization = $this->organizationsModel->find($exercise['org_id']);
        if (!$organization) {
            return redirect()->to('/applicant/jobs')->with('error', 'Organization not found.');
        }

        // Get applicant ID from session
        $applicant_id = session()->get('applicant_id');

        // Get all applied positions for this applicant
        $appliedPositions = [];
        if ($applicant_id) {
            $applications = $this->applicationsModel
                ->where('applicant_id', $applicant_id)
                ->findAll();
            $appliedPositions = array_column($applications, 'position_id');
        }

        // Get all position groups for this exercise
        $positionGroups = $this->positionGroupsModel
            ->where('exercise_id', $exerciseId)
            ->findAll();

        // Initialize array to store positions by group
        $groupedPositions = [];

        // Get positions for each group
        if (!empty($positionGroups)) {
            foreach ($positionGroups as $group) {
                $positions = $this->positionsModel
                    ->where('position_group_id', $group['id'])
                    ->where('status', 'active')
                    ->findAll();

                if (!empty($positions)) {
                    // Mark positions as applied if necessary
                    foreach ($positions as &$position) {
                        $position['has_applied'] = in_array($position['id'], $appliedPositions);
                    }
                    $groupedPositions[$group['group_name']] = $positions;
                }
            }

            // Get positions without a group (if any)
            $groupIds = array_column($positionGroups, 'id');
            if (!empty($groupIds)) {
                $ungroupedPositions = $this->positionsModel
                    ->where('org_id', $exercise['org_id'])
                    ->where('status', 'active')
                    ->whereNotIn('position_group_id', $groupIds)
                    ->findAll();

                if (!empty($ungroupedPositions)) {
                    // Mark ungrouped positions as applied if necessary
                    foreach ($ungroupedPositions as &$position) {
                        $position['has_applied'] = in_array($position['id'], $appliedPositions);
                    }
                    $groupedPositions['Other Positions'] = $ungroupedPositions;
                }
            }
        } else {
            // If no groups exist, get all positions for this exercise
            $allPositions = $this->positionsModel
                ->where('org_id', $exercise['org_id'])
                ->where('status', 'active')
                ->findAll();

            if (!empty($allPositions)) {
                // Mark positions as applied if necessary
                foreach ($allPositions as &$position) {
                    $position['has_applied'] = in_array($position['id'], $appliedPositions);
                }
                $groupedPositions['All Positions'] = $allPositions;
            }
        }

        return view('applicant/applicant_exercise_details', [
            'title' => 'Exercise Details',
            'menu' => 'jobs',
            'exercise' => $exercise,
            'organization' => $organization,
            'positions' => $groupedPositions
        ]);
    }

    public function position($positionId)
    {
        // Get position details
        $position = $this->positionsModel
            ->where('id', $positionId)
            ->where('status', 'active')
            ->first();

        if (!$position) {
            return redirect()->to('/applicant/jobs')->with('error', 'Position not found or not active.');
        }

        // Get organization details
        $organization = $this->organizationsModel->find($position['org_id']);
        if (!$organization) {
            return redirect()->to('/applicant/jobs')->with('error', 'Organization not found.');
        }

        // Get exercise details
        $exercise = $this->exercisesModel
            ->where('org_id', $position['org_id'])
            ->where('status', 'publish')
            ->first();

        if (!$exercise) {
            return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
        }

        // Get applicant details
        $applicantModel = new \App\Models\applicantsModel();
        $applicant = $applicantModel->find(session()->get('applicant_id'));

        // Check if applicant has already applied for this position
        $existingApplication = $this->applicationsModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->where('position_id', $positionId)
            ->first();

        return view('applicant/applicant_position_details', [
            'title' => $position['designation'],
            'menu' => 'jobs',
            'position' => $position,
            'organization' => $organization,
            'exercise' => $exercise,
            'applicant' => $applicant,
            'has_applied' => !empty($existingApplication),
            'application' => $existingApplication
        ]);
    }

    public function apply($positionId)
    {
        try {
            if (!$this->request->isAJAX()) {
                log_message('error', 'Job application: Non-AJAX request received');
                return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
            }

            // Check if position exists and is active
            $position = $this->positionsModel
                ->where('id', $positionId)
                ->where('status', 'active')
                ->first();

            if (!$position) {
                log_message('error', "Job application: Position {$positionId} not found or not active");
                return $this->response->setJSON(['success' => false, 'message' => 'Position not found or not active']);
            }

            // Check if already applied
            $existingApplication = $this->applicationsModel
                ->where('applicant_id', session()->get('applicant_id'))
                ->where('position_id', $positionId)
                ->first();

            if ($existingApplication) {
                log_message('error', "Job application: Duplicate application for position {$positionId} by applicant " . session()->get('applicant_id'));
                return $this->response->setJSON(['success' => false, 'message' => 'You have already applied for this position']);
            }

            // Get applicant details
            $applicantModel = new \App\Models\applicantsModel();
            $applicant = $applicantModel->find(session()->get('applicant_id'));

            if (!$applicant) {
                log_message('error', "Job application: Applicant " . session()->get('applicant_id') . " not found");
                return $this->response->setJSON(['success' => false, 'message' => 'Applicant information not found']);
            }

            // Generate unique application number
            $applicationNumber = 'APP' . date('Y') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

            // Create upload directory if it doesn't exist
            $uploadPath = FCPATH . 'public/uploads/applications';
            if (!is_dir($uploadPath)) {
                if (!mkdir($uploadPath, 0777, true)) {
                    log_message('error', "Job application: Failed to create upload directory {$uploadPath}");
                    throw new \Exception('Failed to create upload directory');
                }
            }

            // Handle file uploads
            $uploadedFiles = [];
            $files = $this->request->getFiles();

            foreach ($files['documents'] as $file) {
                if ($file->isValid() && !$file->hasMoved()) {
                    $newName = $file->getRandomName();

                    // Validate file size (100MB limit)
                    if ($file->getSizeByUnit('mb') > 100) {
                        log_message('error', "Job application: File size exceeds limit - {$file->getName()}");
                        throw new \Exception('File size must not exceed 100MB');
                    }

                    // Validate file type (PDF only)
                    if ($file->getMimeType() !== 'application/pdf') {
                        log_message('error', "Job application: Invalid file type - {$file->getName()}");
                        throw new \Exception('Only PDF files are allowed');
                    }

                    try {
                        $file->move($uploadPath, $newName);
                        $uploadedFiles[] = 'public/uploads/applications/' . $newName;
                    } catch (\Exception $e) {
                        log_message('error', "Job application: File upload failed - {$e->getMessage()}");
                        throw new \Exception('Failed to upload file: ' . $e->getMessage());
                    }
                }
            }

            if (empty($uploadedFiles)) {
                log_message('error', 'Job application: No files uploaded');
                throw new \Exception('Please upload at least one document');
            }

            // Prepare application data
            $applicationData = [
                'applicant_id' => session()->get('applicant_id'),
                'position_id' => $positionId,
                'application_number' => $applicationNumber,
                'fname' => $applicant['fname'],
                'lname' => $applicant['lname'],
                'gender' => $applicant['gender'],
                'dobirth' => $applicant['dobirth'],
                'place_of_origin' => $applicant['place_of_origin'],
                'id_photo_path' => $applicant['id_photo_path'],
                'contact_details' => $applicant['contact_details'],
                'location_address' => $applicant['location_address'],
                'id_numbers' => $applicant['id_numbers'],
                'current_employer' => $applicant['current_employer'],
                'current_position' => $applicant['current_position'],
                'current_salary' => $applicant['current_salary'],
                'citizenship' => $applicant['citizenship'],
                'marital_status' => $applicant['marital_status'],
                'date_of_marriage' => $applicant['date_of_marriage'],
                'spouse_employer' => $applicant['spouse_employer'],
                'children' => $applicant['children'],
                'offence_convicted' => $applicant['offence_convicted'],
                'referees' => $applicant['referees'],
                'how_did_you_hear_about_us' => $applicant['how_did_you_hear_about_us'],
                'signature_path' => $applicant['signature_path'],
                'publications' => $applicant['publications'],
                'awards' => $applicant['awards'],
                'status' => 'pending',
                'created_by' => session()->get('applicant_id')
            ];

            // Start transaction using model
            $this->applicationsModel->db->transStart();

            try {
                // Insert application using model
                if (!$this->applicationsModel->insert($applicationData)) {
                    $error = $this->applicationsModel->errors();
                    log_message('error', 'Job application: Database insert failed - ' . json_encode($error));
                    throw new \Exception('Database error: ' . json_encode($error));
                }

                $applicationId = $this->applicationsModel->getInsertID();

                // Insert files using model
                foreach ($uploadedFiles as $filePath) {
                    $fileData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'file_title' => 'Application Document',
                        'file_path' => $filePath,
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationFilesModel->insert($fileData)) {
                        throw new \Exception('Failed to save file information');
                    }
                }

                // Copy experiences data
                $experiences = $this->applicantExperiencesModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($experiences as $experience) {
                    $experienceData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'employer' => $experience['employer'],
                        'employer_contacts_address' => $experience['employer_contacts_address'],
                        'position' => $experience['position'],
                        'date_from' => $experience['date_from'],
                        'date_to' => $experience['date_to'],
                        'achievements' => $experience['achievements'],
                        'work_description' => $experience['work_description'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationExperiencesModel->insert($experienceData)) {
                        throw new \Exception('Failed to copy experience data');
                    }
                }

                // Copy education data
                $educationRecords = $this->applicantEducationModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($educationRecords as $education) {
                    $educationData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'institution' => $education['institution'],
                        'course' => $education['course'],
                        'date_from' => $education['date_from'],
                        'date_to' => $education['date_to'],
                        'education_level' => $education['education_level'],
                        'units' => $education['units'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationEducationModel->insert($educationData)) {
                        throw new \Exception('Failed to copy education data');
                    }
                }

                // Copy existing files data
                $existingFiles = $this->applicantFilesModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($existingFiles as $file) {
                    $existingFileData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'file_title' => $file['file_title'],
                        'file_description' => $file['file_description'],
                        'file_path' => $file['file_path'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationFilesModel->insert($existingFileData)) {
                        throw new \Exception('Failed to copy existing file data');
                    }
                }

                // Complete transaction
                $this->applicationsModel->db->transComplete();

                if ($this->applicationsModel->db->transStatus() === false) {
                    throw new \Exception('Transaction failed');
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Application submitted successfully'
                ]);

            } catch (\Exception $e) {
                // Rollback transaction using model
                $this->applicationsModel->db->transRollback();
                log_message('error', 'Job application: Transaction failed - ' . $e->getMessage());

                // Delete uploaded files if transaction fails
                foreach ($uploadedFiles as $file) {
                    $fullPath = FCPATH . $file;
                    if (file_exists($fullPath)) {
                        unlink($fullPath);
                    }
                }

                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error submitting application: ' . $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Job application: Unexpected error - ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An unexpected error occurred. Please try again later.'
            ]);
        }
    }

    /**
     * Download application file
     *
     * @param string $filename
     * @return mixed
     */
    public function downloadFile($filename)
    {
        // Security check: Verify the file belongs to this applicant
        $file = $this->appxApplicationFilesModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->where('file_path', 'public/uploads/applications/' . $filename)
            ->first();

        if (!$file) {
            return $this->response->setStatusCode(403)->setBody('Access denied');
        }

        $path = FCPATH . 'public/uploads/applications/' . $filename;

        if (!file_exists($path)) {
            return $this->response->setStatusCode(404)->setBody('File not found');
        }

        return $this->response->download($path, null)->setFileName($filename);
    }

    /**
     * Display list of applications for the logged-in applicant
     */
    public function applications()
    {
        // Get all applications for this applicant
        $applications = $this->applicationsModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->findAll();

        // Get position details for each application
        $applicationData = [];
        foreach ($applications as $application) {
            $position = $this->positionsModel->find($application['position_id']);
            if ($position) {
                $organization = $this->organizationsModel->find($position['org_id']);
                $applicationData[] = [
                    'application' => $application,
                    'position' => $position,
                    'organization' => $organization
                ];
            }
        }

        return view('applicant/applicant_applications', [
            'title' => 'My Applications',
            'menu' => 'applications',
            'applications' => $applicationData
        ]);
    }
}