<?php

namespace App\Controllers;

class ApplicantController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info', 'application']);
        $this->session = session();
    }

    public function dashboard()
    {
        // Mock applicant data for UI development
        $applicant = [
            'fname' => 'Demo',
            'lname' => 'Applicant',
            'email' => '<EMAIL>',
            'gender' => 'Male',
            'dobirth' => '1990-01-01',
            'place_of_origin' => 'Port Moresby',
            'contact_details' => '+675 123 4567',
            'location_address' => '123 Main Street, Port Moresby'
        ];

        // Mock application statistics
        $total_applications = 5;
        $pending_applications = 2;
        $shortlisted_applications = 2;
        $rejected_applications = 1;

        // Mock recent applications
        $recent_applications = [
            [
                'id' => 1,
                'position_title' => 'Software Developer',
                'department' => 'Information Technology',
                'created_at' => '2024-01-15',
                'status' => 'pending'
            ],
            [
                'id' => 2,
                'position_title' => 'Data Analyst',
                'department' => 'Finance',
                'created_at' => '2024-01-10',
                'status' => 'shortlisted'
            ],
            [
                'id' => 3,
                'position_title' => 'Project Manager',
                'department' => 'Operations',
                'created_at' => '2024-01-05',
                'status' => 'rejected'
            ]
        ];

        // Mock latest job openings
        $latest_jobs = [
            [
                'id' => 1,
                'title' => 'Senior Developer',
                'department' => 'IT Department',
                'location' => 'Port Moresby',
                'posted_date' => '2024-01-20'
            ],
            [
                'id' => 2,
                'title' => 'Marketing Specialist',
                'department' => 'Marketing',
                'location' => 'Lae',
                'posted_date' => '2024-01-18'
            ],
            [
                'id' => 3,
                'title' => 'HR Officer',
                'department' => 'Human Resources',
                'location' => 'Mount Hagen',
                'posted_date' => '2024-01-16'
            ]
        ];

        return view('applicant/applicant_dashboard', [
            'title' => 'Dashboard',
            'menu' => 'dashboard',
            'applicant' => $applicant,
            'total_applications' => $total_applications,
            'pending_applications' => $pending_applications,
            'shortlisted_applications' => $shortlisted_applications,
            'rejected_applications' => $rejected_applications,
            'recent_applications' => $recent_applications,
            'latest_jobs' => $latest_jobs
        ]);
    }

    public function profile()
    {
        // Mock applicant data for UI development
        $applicant = [
            'id' => 1,
            'fname' => 'Demo',
            'lname' => 'Applicant',
            'email' => '<EMAIL>',
            'gender' => 'Male',
            'dobirth' => '1990-01-01',
            'place_of_origin' => 'Port Moresby, NCD',
            'contact_details' => '+675 123 4567',
            'location_address' => '123 Main Street, Port Moresby, NCD',
            'id_photo_path' => '', // Empty to show default avatar
            'current_employer' => 'Tech Solutions PNG',
            'current_position' => 'Software Developer',
            'current_salary' => 'K55,000',
            'citizenship' => 'Papua New Guinea',
            'id_numbers' => 'NID: *********, Passport: P1234567',
            'offence_convicted' => '',
            'how_did_you_hear_about_us' => 'website',
            'marital_status' => 'Single',
            'date_of_marriage' => '',
            'spouse_employer' => '',
            'children' => '',
            'referees' => 'John Smith - Manager, Tech Solutions PNG, +675 987 6543',
            'signature_path' => '',
            'publications' => '',
            'awards' => ''
        ];

        // Mock work experience data
        $experiences = [
            [
                'id' => 1,
                'position' => 'Software Developer',
                'employer' => 'Tech Solutions PNG',
                'employer_contacts_address' => 'Level 3, Vision City, Port Moresby, NCD, +675 321 9876',
                'date_from' => '2022-01-15',
                'date_to' => '', // Current position
                'work_description' => 'Developing web applications using PHP, JavaScript, and MySQL. Working on government projects and private sector solutions.',
                'achievements' => 'Led development of 3 major projects, improved system performance by 40%, mentored 2 junior developers.'
            ],
            [
                'id' => 2,
                'position' => 'Junior Developer',
                'employer' => 'Digital PNG Ltd',
                'employer_contacts_address' => 'Harbour City, Port Moresby, NCD, +675 456 7890',
                'date_from' => '2020-06-01',
                'date_to' => '2021-12-31',
                'work_description' => 'Frontend development using HTML, CSS, JavaScript. Maintained existing systems and developed new features.',
                'achievements' => 'Completed 15+ projects on time, received employee of the month award twice.'
            ],
            [
                'id' => 3,
                'position' => 'IT Intern',
                'employer' => 'Government IT Department',
                'employer_contacts_address' => 'Waigani, Port Moresby, NCD, +675 111 2222',
                'date_from' => '2019-11-01',
                'date_to' => '2020-05-31',
                'work_description' => 'Assisted with system maintenance, data entry, and basic programming tasks.',
                'achievements' => 'Successfully completed internship program, gained practical experience in government systems.'
            ]
        ];

        // Mock education data
        $education = [
            [
                'id' => 1,
                'institution' => 'University of Papua New Guinea',
                'course' => 'Bachelor of Computer Science',
                'education_level' => 3, // Bachelor's degree
                'units' => 'Programming Fundamentals, Database Systems, Web Development, Software Engineering, Data Structures',
                'date_from' => '2016-02-01',
                'date_to' => '2019-11-30'
            ],
            [
                'id' => 2,
                'institution' => 'Port Moresby Technical College',
                'course' => 'Diploma in Information Technology',
                'education_level' => 2, // Diploma
                'units' => 'Computer Networks, Programming, Database Management, System Administration',
                'date_from' => '2014-02-01',
                'date_to' => '2015-11-30'
            ],
            [
                'id' => 3,
                'institution' => 'Port Moresby High School',
                'course' => 'Grade 12 Certificate',
                'education_level' => 1, // High School
                'units' => 'Mathematics, Physics, Chemistry, English, Computer Studies',
                'date_from' => '2012-02-01',
                'date_to' => '2013-11-30'
            ]
        ];

        // Mock education levels
        $education_levels = [
            1 => 'High School',
            2 => 'Diploma/Certificate',
            3 => 'Bachelor\'s Degree',
            4 => 'Master\'s Degree',
            5 => 'PhD/Doctorate'
        ];

        // Mock files data
        $files = [
            [
                'id' => 1,
                'file_title' => 'Resume/CV',
                'file_description' => 'Updated resume with latest experience',
                'file_path' => 'public/uploads/applicants/resume_demo.pdf',
                'created_at' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'file_title' => 'University Transcript',
                'file_description' => 'Official transcript from UPNG',
                'file_path' => 'public/uploads/applicants/transcript_demo.pdf',
                'created_at' => '2024-01-15 10:35:00'
            ],
            [
                'id' => 3,
                'file_title' => 'Certificates',
                'file_description' => 'Professional certifications and training certificates',
                'file_path' => 'public/uploads/applicants/certificates_demo.pdf',
                'created_at' => '2024-01-15 10:40:00'
            ]
        ];

        return view('applicant/applicant_profile', [
            'title' => 'Profile',
            'menu' => 'profile',
            'applicant' => $applicant,
            'experiences' => $experiences,
            'education' => $education,
            'education_levels' => $education_levels,
            'education_data' => [], // Empty array for alternative education data source
            'files' => $files
        ]);
    }
}