<?php

namespace App\Controllers;

class HomeAuthController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    // Admin Login Methods
    public function loginForm()
    {
        return view('home/home_login', [
            'title' => 'Admin Login',
            'menu' => 'login'
        ]);
    }

    public function processLogin()
    {
        if (!$this->request->getPost('username') || !$this->request->getPost('password')) {
            return redirect()->back()->with('error', 'Username and password are required');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        $user = $this->usersModel->where('username', $username)->first();

        if ($user && password_verify($password, $user['password'])) {
            if ($user['status'] != 1) {
                return redirect()->back()->with('error', 'Your account is inactive. Please contact administrator.');
            }

            $this->session->set([
                'logged_in' => true,
                'user_id' => $user['id'],
                'name' => $user['name'],
                'role' => $user['role'],
                'org_id' => $user['org_id']
            ]);

            return redirect()->to('dashboard')->with('success', "Welcome back, {$user['name']}! You've successfully logged in.");
        }

        return redirect()->back()->with('error', 'Invalid username or password. Please try again.');
    }

    // Applicant Registration Methods
    public function registerForm()
    {
        return view('home/home_register', [
            'title' => 'Create Account',
            'menu' => 'register'
        ]);
    }

    public function processRegister()
    {
        $rules = [
            'firstname' => 'required|min_length[2]|max_length[50]',
            'lastname' => 'required|min_length[2]|max_length[50]',
            'email' => 'required|valid_email|is_unique[applicants.email]',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Generate unique ID and activation token
        $unique_id = 'APP' . time() . rand(1000, 9999);
        $activation_token = bin2hex(random_bytes(32));

        // Prepare data for insertion
        $data = [
            'unique_id' => $unique_id,
            'fname' => $this->request->getPost('firstname'),
            'lname' => $this->request->getPost('lastname'),
            'email' => $this->request->getPost('email'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'activation_token' => $activation_token,
            'status' => 'pending'
        ];

        try {
            // Save applicant data
            $inserted = $this->applicantsModel->insert($data);

            if (!$inserted) {
                return redirect()->back()->withInput()->with('error', 'Failed to create account. Please try again.');
            }

            // For UI development, we'll skip email sending and just show success
            session()->setFlashdata('swal_icon', 'success');
            session()->setFlashdata('swal_title', 'Account Created');
            session()->setFlashdata('swal_text', 'Your account has been created successfully. Please check your email for activation instructions.');

            return redirect()->to('/');

        } catch (\Exception $e) {
            log_message('error', 'Registration error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating your account. Please try again.');
        }
    }

    // Applicant Login Methods
    public function applicantLoginForm()
    {
        return view('home/home_applicant_login', [
            'title' => 'Applicant Login',
            'menu' => 'applicant_login'
        ]);
    }

    public function processApplicantLogin()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        if (!$email || !$password) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Missing Information');
            session()->setFlashdata('swal_text', 'Please enter both email and password.');
            return redirect()->back();
        }

        $applicant = $this->applicantsModel->where('email', $email)->first();

        if ($applicant && password_verify($password, $applicant['password'])) {
            if ($applicant['status'] !== 'active') {
                session()->setFlashdata('swal_icon', 'warning');
                session()->setFlashdata('swal_title', 'Account Not Activated');
                session()->setFlashdata('swal_text', 'Please activate your account first. Check your email for the activation link.');
                return redirect()->back();
            }

            $this->session->set([
                'logged_in' => true,
                'applicant_id' => $applicant['applicant_id'],
                'applicant_name' => $applicant['fname'] . ' ' . $applicant['lname'],
                'applicant_email' => $applicant['email']
            ]);

            return redirect()->to('applicant/dashboard')->with('success', "Welcome back, {$applicant['fname']}!");
        }

        session()->setFlashdata('swal_icon', 'error');
        session()->setFlashdata('swal_title', 'Login Failed');
        session()->setFlashdata('swal_text', 'Invalid email or password. Please try again.');
        return redirect()->back();
    }

    // Account Activation
    public function activate($token)
    {
        $applicant = $this->applicantsModel->where('activation_token', $token)->first();

        if (!$applicant) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Invalid Token');
            session()->setFlashdata('swal_text', 'The activation token is invalid or has expired.');
            return redirect()->to('/');
        }

        if ($applicant['status'] === 'active') {
            session()->setFlashdata('swal_icon', 'info');
            session()->setFlashdata('swal_title', 'Already Activated');
            session()->setFlashdata('swal_text', 'Your account is already activated. Please login.');
            return redirect()->to('/');
        }

        // Activate the account
        $this->applicantsModel->update($applicant['applicant_id'], [
            'status' => 'active',
            'activation_token' => null
        ]);

        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Account Activated');
        session()->setFlashdata('swal_text', 'Your account has been activated successfully. You can now login.');
        return redirect()->to('/');
    }
}
