<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;

class IncomingApplicantsController extends BaseController
{
    use ResponseTrait;

    protected $session;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = \Config\Services::session();
    }

    /**
     * Display the list of unacknowledged applications
     */
    public function index()
    {
        // Dummy data for UI development - replace with actual model calls later
        $applications = [
            [
                'id' => 1,
                'application_number' => 'APP-2024-001',
                'applicant_id' => 101,
                'fname' => 'John',
                'lname' => 'Doe',
                'email' => '<EMAIL>',
                'position_id' => 1,
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-10 09:00:00',
                'status' => 'pending'
            ],
            [
                'id' => 2,
                'application_number' => 'APP-2024-002',
                'applicant_id' => 102,
                'fname' => 'Jane',
                'lname' => '<PERSON>',
                'email' => '<EMAIL>',
                'position_id' => 2,
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-09 11:15:00',
                'status' => 'pending'
            ],
            [
                'id' => 3,
                'application_number' => 'APP-2024-003',
                'applicant_id' => 103,
                'fname' => 'Michael',
                'lname' => 'Johnson',
                'email' => '<EMAIL>',
                'position_id' => 3,
                'recieved_acknowledged' => null,
                'created_at' => '2024-01-08 13:30:00',
                'status' => 'pending'
            ]
        ];

        $data = [
            'title' => 'Incoming Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('incoming_applicants/incoming_applicants_list', $data);
    }

    /**
     * Mark an application as received/acknowledged
     */
    public function acknowledge($id)
    {
        $data = [
            'recieved_acknowledged' => date('Y-m-d H:i:s'),
            'recieved_by' => $this->session->get('user_id') ?? 1,
            'recieved_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id') ?? 1,
            'status' => 'active'
        ];

        try {
            // Simulate successful update for UI development
            $updateSuccess = true; // Replace with actual model update call later

            if ($updateSuccess) {
                // Dummy application data for email notification
                $application = [
                    'id' => $id,
                    'application_number' => 'APP-2024-' . str_pad($id, 3, '0', STR_PAD_LEFT),
                    'applicant_id' => 101,
                    'fname' => 'John',
                    'lname' => 'Doe',
                    'email' => '<EMAIL>'
                ];

                // Simulate email sending for UI development
                $emailSent = true; // Replace with actual email sending later
                log_message('debug', 'Acknowledgment email result: ' . ($emailSent ? 'success' : 'failed'));

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Application successfully acknowledged',
                    'email_sent' => $emailSent,
                    'csrf_hash' => csrf_hash()
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to acknowledge application',
                    'csrf_hash' => csrf_hash()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error acknowledging application: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    /**
     * Send acknowledgment email to applicant
     */
    private function sendAcknowledgmentEmail($application)
    {
        try {
            // Simulate email sending for UI development - replace with actual email logic later
            log_message('info', 'Simulated acknowledgment email sent to: ' . $application['email']);
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Exception while sending acknowledgment email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * View application details
     */
    public function view($id)
    {
        // Dummy application data for UI development
        $application = [
            'id' => $id,
            'application_number' => 'APP-2024-' . str_pad($id, 3, '0', STR_PAD_LEFT),
            'applicant_id' => 101,
            'fname' => 'John',
            'lname' => 'Doe',
            'email' => '<EMAIL>',
            'position_id' => 1,
            'recieved_acknowledged' => null,
            'created_at' => '2024-01-10 09:00:00',
            'status' => 'pending'
        ];

        if (!$application) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Application not found',
                'csrf_hash' => csrf_hash()
            ]);
        }

        $data = [
            'application' => $application
        ];

        return $this->response->setJSON([
            'success' => true,
            'html' => view('incoming_applicants/incoming_applicants_view_modal', $data),
            'csrf_hash' => csrf_hash()
        ]);
    }

    /**
     * Batch acknowledge multiple applications
     */
    public function batchAcknowledge()
    {
        $ids = $this->request->getPost('ids');

        if (empty($ids)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No applications selected',
                'csrf_hash' => csrf_hash()
            ]);
        }

        $data = [
            'recieved_acknowledged' => date('Y-m-d H:i:s'),
            'recieved_by' => $this->session->get('user_id') ?? 1,
            'recieved_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id') ?? 1,
            'status' => 'active'
        ];

        $successCount = 0;
        $failCount = 0;
        $emailSentCount = 0;

        foreach ($ids as $id) {
            try {
                // Simulate successful update for UI development
                $updateSuccess = true; // Replace with actual model update call later

                if ($updateSuccess) {
                    $successCount++;

                    // Dummy application data for email notification
                    $application = [
                        'id' => $id,
                        'application_number' => 'APP-2024-' . str_pad($id, 3, '0', STR_PAD_LEFT),
                        'applicant_id' => 101,
                        'fname' => 'John',
                        'lname' => 'Doe',
                        'email' => '<EMAIL>'
                    ];

                    // Simulate email sending
                    if ($application) {
                        $emailSent = $this->sendAcknowledgmentEmail($application);
                        if ($emailSent) $emailSentCount++;
                    }
                } else {
                    $failCount++;
                }
            } catch (\Exception $e) {
                log_message('error', 'Error in batch acknowledge for ID ' . $id . ': ' . $e->getMessage());
                $failCount++;
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => "$successCount applications acknowledged successfully. $failCount failed. $emailSentCount email notifications sent.",
            'csrf_hash' => csrf_hash()
        ]);
    }
}