<?php

namespace App\Controllers;

use CodeIgniter\Database\Exceptions\DatabaseException;

class PositionsController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    public function positions_groups($exercise_id)
    {
        $exercise = $this->exerciseModel->find($exercise_id);
        if (!$exercise) {
            return redirect()->to('positions/positions_exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get all position groups for this exercise with parent name
        $groups = $this->positionsGroupModel
            ->select('positions_groups.*, parent.group_name as parent_name')
            ->join('positions_groups as parent', 'parent.id = positions_groups.parent_id', 'left')
            ->where('positions_groups.org_id', $this->session->get('org_id'))
            ->where('positions_groups.exercise_id', $exercise_id)
            ->findAll();

        // Count positions for each group
        foreach ($groups as &$group) {
            $group['position_count'] = $this->positionsModel
                                            ->where('position_group_id', $group['id'])
                                            ->countAllResults();
        }

        $data = [
            'title' => 'Position Groups',
            'menu' => 'positions',
            'exercise' => $exercise,
            'positions_groups' => $groups
        ];

        return view('positions/positions_group', $data);
    }

    public function addPositionGroup()
    {
        $exercise_id = $this->request->getPost('exercise_id');
        if (!$exercise_id) {
            session()->setFlashdata('error', 'Exercise ID is required');
            return redirect()->to('positions/positions_exercises');
        }

        $data = [
            'org_id' => $this->session->get('org_id'),
            'exercise_id' => $exercise_id,
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'group_name' => $this->request->getPost('group_name'),
            'description' => $this->request->getPost('description'),
            'created_by' => $this->session->get('user_id')
        ];

        try {
            if ($this->positionsGroupModel->save($data)) {
                session()->setFlashdata('success', 'Position group added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add position group');
            }
        } catch (DatabaseException $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                session()->setFlashdata('error', 'A position group with this name already exists in this exercise');
            } else {
                session()->setFlashdata('error', 'Failed to add position group: ' . $e->getMessage());
            }
        }

        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function updatePositionGroup()
    {
        $id = $this->request->getPost('id');
        $exercise_id = $this->request->getPost('exercise_id');

        // Don't allow setting itself as parent
        $parent_id = $this->request->getPost('parent_id');
        if ($parent_id == $id) {
            session()->setFlashdata('error', 'A group cannot be its own parent');
            return redirect()->to('positions/positions_groups/' . $exercise_id);
        }

        $data = [
            'parent_id' => $parent_id ?: null,
            'group_name' => $this->request->getPost('group_name'),
            'description' => $this->request->getPost('description'),
            'updated_by' => $this->session->get('user_id')
        ];

        try {
            if ($this->positionsGroupModel->update($id, $data)) {
                session()->setFlashdata('success', 'Position group updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update position group');
            }
        } catch (DatabaseException $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                session()->setFlashdata('error', 'A position group with this name already exists in this exercise');
            } else {
                session()->setFlashdata('error', 'Failed to update position group: ' . $e->getMessage());
            }
        }

        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function deletePositionGroup($id)
    {
        // Get exercise_id before deletion for redirect
        $group = $this->positionsGroupModel->find($id);
        $exercise_id = $group['exercise_id'];

        // Check if any groups are using this as parent
        $hasChildren = $this->positionsGroupModel
            ->where('parent_id', $id)
            ->where('exercise_id', $exercise_id)
            ->countAllResults() > 0;

        if ($hasChildren) {
            session()->setFlashdata('error', 'Cannot delete group that has child groups');
            return redirect()->to('positions/positions_groups/' . $exercise_id);
        }

        try {
            if ($this->positionsGroupModel->delete($id)) {
                session()->setFlashdata('success', 'Position group deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete position group');
            }
        } catch (DatabaseException $e) {
            session()->setFlashdata('error', 'Failed to delete position group: ' . $e->getMessage());
        }

        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function positions_exercises()
    {
        // Get all exercises in draft status
        $exerciseModel = new \App\Models\ExerciseModel();
        $exercises = $exerciseModel->where('org_id', session()->get('org_id'))
                                  ->where('status', 'draft')
                                  ->findAll();

        // Get position groups and positions count for each exercise
        foreach ($exercises as &$exercise) {
            // Count position groups for this exercise
            $exercise['group_count'] = $this->positionsGroupModel
                                            ->where('exercise_id', $exercise['id'])
                                            ->countAllResults();

            // Get all position group IDs for this exercise
            $groupIds = $this->positionsGroupModel
                             ->select('id')
                             ->where('exercise_id', $exercise['id'])
                             ->findAll();

            // Extract just the IDs into an array
            $groupIdArray = array_column($groupIds, 'id');

            // Count positions across all groups in this exercise
            $exercise['position_count'] = empty($groupIdArray) ? 0 :
                                          $this->positionsModel
                                               ->whereIn('position_group_id', $groupIdArray)
                                               ->countAllResults();
        }

        return view('positions/positions_exercises', [
            'title' => 'Position Exercises',
            'menu' => 'positions',
            'exercises' => $exercises
        ]);
    }

    public function view_positions($group_id)
    {
        $data = [
            'title' => 'View Positions',
            'menu' => 'positions',
            'group_id' => $group_id,
            'group' => $this->positionsGroupModel->find($group_id),
            'positions' => $this->positionsModel->where('position_group_id', $group_id)->findAll()
        ];

        return view('positions/positions_view', $data);
    }

    public function addPosition()
    {
        // Handle file upload
        $jdFile = $this->request->getFile('jd_file');
        $jdFilepath = null;

        if ($jdFile && $jdFile->isValid() && !$jdFile->hasMoved()) {
            if ($jdFile->getClientMimeType() !== 'application/pdf') {
                return $this->response->setJSON(['success' => false, 'message' => 'Only PDF files are allowed for JD upload']);
            }

            // Generate unique filename
            $newName = $jdFile->getRandomName();

            // Move file to uploads directory
            try {
                $jdFile->move(FCPATH . 'uploads/jd_files', $newName);
                $jdFilepath = 'public/uploads/jd_files/' . $newName;
            } catch (\Exception $e) {
                return $this->response->setJSON(['success' => false, 'message' => 'Failed to upload JD file']);
            }
        }

        $data = [
            'org_id' => $this->session->get('org_id'),
            'position_group_id' => $this->request->getPost('position_group_id'),
            'position_reference' => $this->request->getPost('position_reference'),
            'designation' => $this->request->getPost('designation'),
            'classification' => $this->request->getPost('classification'),
            'award' => $this->request->getPost('award'),
            'location' => $this->request->getPost('location'),
            'annual_salary' => $this->request->getPost('annual_salary'),
            'qualifications' => $this->request->getPost('qualifications'),
            'knowledge' => $this->request->getPost('knowledge'),
            'skills_competencies' => $this->request->getPost('skills_competencies'),
            'job_experiences' => $this->request->getPost('job_experiences'),
            'status' => $this->request->getPost('status'),
            'remarks' => $this->request->getPost('remarks'),
            'jd_filepath' => $jdFilepath,
            'created_by' => $this->session->get('user_id')
        ];

        if ($this->positionsModel->save($data)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Position added successfully']);
        }
        return $this->response->setJSON(['success' => false, 'message' => 'Failed to add position']);
    }

    public function updatePosition()
    {
        $id = $this->request->getPost('id');

        // Handle file upload
        $jdFile = $this->request->getFile('jd_file');
        $jdFilepath = null;

        if ($jdFile && $jdFile->isValid() && !$jdFile->hasMoved()) {
            if ($jdFile->getClientMimeType() !== 'application/pdf') {
                return $this->response->setJSON(['success' => false, 'message' => 'Only PDF files are allowed for JD upload']);
            }

            // Get existing position data
            $existingPosition = $this->positionsModel->find($id);

            // Delete old file if exists
            if (!empty($existingPosition['jd_filepath'])) {
                $oldFilePath = FCPATH . $existingPosition['jd_filepath'];
                if (file_exists($oldFilePath)) {
                    unlink($oldFilePath);
                }
            }

            // Generate unique filename
            $newName = $jdFile->getRandomName();

            // Move file to uploads directory
            try {
                $jdFile->move(FCPATH . 'uploads/jd_files', $newName);
                $jdFilepath = 'public/uploads/jd_files/' . $newName;
            } catch (\Exception $e) {
                return $this->response->setJSON(['success' => false, 'message' => 'Failed to upload JD file: ' . $e->getMessage()]);
            }
        }

        $data = [
            'position_reference' => $this->request->getPost('position_reference'),
            'designation' => $this->request->getPost('designation'),
            'classification' => $this->request->getPost('classification'),
            'award' => $this->request->getPost('award'),
            'location' => $this->request->getPost('location'),
            'annual_salary' => $this->request->getPost('annual_salary'),
            'qualifications' => $this->request->getPost('qualifications'),
            'knowledge' => $this->request->getPost('knowledge'),
            'skills_competencies' => $this->request->getPost('skills_competencies'),
            'job_experiences' => $this->request->getPost('job_experiences'),
            'status' => $this->request->getPost('status'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $this->session->get('user_id')
        ];

        // Only update jd_filepath if a new file was uploaded
        if ($jdFilepath) {
            $data['jd_filepath'] = $jdFilepath;
        }

        try {
            if ($this->positionsModel->update($id, $data)) {
                return $this->response->setJSON(['success' => true, 'message' => 'Position updated successfully']);
            }

            // Get validation errors if any
            $errors = $this->positionsModel->errors();
            if (!empty($errors)) {
                return $this->response->setJSON(['success' => false, 'message' => 'Validation failed', 'errors' => $errors]);
            }

            return $this->response->setJSON(['success' => false, 'message' => 'Failed to update position']);
        } catch (\Exception $e) {
            log_message('error', 'Position update error: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Error updating position: ' . $e->getMessage()]);
        }
    }

    public function deletePosition($id)
    {
        if ($this->positionsModel->delete($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Position deleted successfully']);
        }
        return $this->response->setJSON(['success' => false, 'message' => 'Failed to delete position']);
    }

    public function downloadJD($id)
    {
        $position = $this->positionsModel->find($id);

        if (!$position || empty($position['jd_filepath'])) {
            return $this->response->setJSON(['success' => false, 'message' => 'JD file not found']);
        }

        $filepath = WRITEPATH . $position['jd_filepath'];

        if (!file_exists($filepath)) {
            return $this->response->setJSON(['success' => false, 'message' => 'JD file not found']);
        }

        return $this->response->download($filepath, null)->setFileName('JD_' . $position['position_reference'] . '.pdf');
    }
}
