<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Exercise Header -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <a href="<?= base_url('applicant/jobs') ?>" class="btn btn-outline-primary mb-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Job Openings
                    </a>
                    <h2 class="card-title mb-1">
                        Exercise #<?= esc($exercise['gazzetted_no']) ?>
                        <span class="text-muted">(<?= esc($exercise['advertisement_no']) ?>)</span>
                    </h2>
                    <p class="text-muted mb-0">
                        <i class="fas fa-building me-1"></i> <?= esc($organization['name']) ?>
                    </p>
                </div>
                <span class="badge bg-success">Published</span>
            </div>
            <div class="row mt-3">
                <div class="col-md-8">
                    <?php if (!empty($exercise['description'])): ?>
                        <p class="card-text"><?= nl2br(esc($exercise['description'])) ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-subtitle mb-2 text-muted">Important Dates</h6>
                            <p class="mb-1">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <strong>Published:</strong> <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?>
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-calendar-check me-2"></i>
                                <strong>Closes:</strong> <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions Section -->
    <?php if (!empty($positions)): ?>
        <?php foreach ($positions as $groupName => $groupPositions): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        <?= esc($groupName) ?>
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="10%">Reference</th>
                                    <th width="20%">Designation</th>
                                    <th width="15%">Classification</th>
                                    <th width="15%">Award</th>
                                    <th width="15%">Annual Salary</th>
                                    <th width="10%">Location</th>
                                    <th width="10%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $count = 1;
                                foreach ($groupPositions as $position): 
                                ?>
                                    <tr <?= !empty($position['has_applied']) ? 'class="table-success"' : '' ?>>
                                        <td><?= $count++ ?></td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?= esc($position['position_reference']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?= esc($position['designation']) ?></strong>
                                            <?php if (!empty($position['has_applied'])): ?>
                                                <span class="badge bg-success ms-2">Applied</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= esc($position['classification']) ?: 'N/A' ?></td>
                                        <td><?= esc($position['award']) ?: 'N/A' ?></td>
                                        <td>
                                            <?php if (!empty($position['annual_salary'])): ?>
                                                K<?= number_format((float)$position['annual_salary'], 2) ?>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($position['location'])): ?>
                                                <?= esc($position['location']) ?>
                                            <?php elseif (!empty($organization['addlockprov'])): ?>
                                                <?= esc($organization['addlockprov']) ?> <small class="text-muted">(org)</small>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <a href="<?= base_url('applicant/jobs/position/' . $position['id']) ?>" 
                                                   class="btn btn-primary btn-sm" 
                                                   title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if (!empty($position['jd_filepath'])): ?>
                                                    <a href="<?= base_url(str_replace('public/', '', $position['jd_filepath'])) ?>"
                                                       class="btn btn-info btn-sm"
                                                       title="Job Description"
                                                       target="_blank">
                                                        <i class="fas fa-file-pdf"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            No positions are currently available for this exercise.
        </div>
    <?php endif; ?>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTables
    if ($.fn.DataTable) {
        $('.table').DataTable({
            "order": [[0, "asc"]],
            "pageLength": 25
        });
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 