<?php
/**
 * View file for listing unacknowledged applications
 * 
 * @var array $applications List of applications pending acknowledgment
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Hidden CSRF token field for AJAX requests -->
    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
    
    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-inbox me-2"></i>Incoming Applications</h2>
            <p class="text-muted">Applications waiting for acknowledgment</p>
        </div>
        <div class="col-md-6 text-end">
            <button id="batchAcknowledgeBtn" class="btn btn-primary" disabled>
                <i class="fas fa-check-circle me-1"></i> Acknowledge Selected
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($applications)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No pending applications found.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="applicationsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th width="10%">App. Number</th>
                                <th width="20%">Applicant Name</th>
                                <th width="15%">Position</th>
                                <th width="15%">Date Applied</th>
                                <th width="15%">Status</th>
                                <th width="20%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $app): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="app-checkbox form-check-input" 
                                               value="<?= $app['id'] ?>">
                                    </td>
                                    <td><?= $app['application_number'] ?></td>
                                    <td><?= $app['fname'] . ' ' . $app['lname'] ?></td>
                                    <td>
                                        <?php 
                                        // Ideally, this would be a joined field, but we'll display the ID for now
                                        echo $app['position_id']; 
                                        ?>
                                    </td>
                                    <td><?= date('d M Y', strtotime($app['created_at'])) ?></td>
                                    <td>
                                        <span class="badge bg-warning">Pending</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info view-btn"
                                                data-id="<?= $app['id'] ?>">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                        <button type="button" class="btn btn-sm btn-success acknowledge-btn"
                                                data-id="<?= $app['id'] ?>">
                                            <i class="fas fa-check-circle"></i> Acknowledge
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- View Application Modal -->
<div class="modal fade" id="viewApplicationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Application Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="viewApplicationModalBody">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="modalAcknowledgeBtn">
                    <i class="fas fa-check-circle me-1"></i> Acknowledge Receipt
                </button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    const table = $('#applicationsTable').DataTable({
        responsive: true,
        order: [[4, 'desc']], // Sort by date applied desc
        language: {
            search: "Search applications:",
            lengthMenu: "Show _MENU_ applications per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applications",
            emptyTable: "No applications available",
        }
    });
    
    // Handle select all checkbox
    $('#selectAll').change(function() {
        const isChecked = $(this).prop('checked');
        $('.app-checkbox').prop('checked', isChecked);
        updateBatchButton();
    });
    
    // Handle individual checkboxes
    $(document).on('change', '.app-checkbox', function() {
        updateBatchButton();
        
        // If any checkbox is unchecked, uncheck the "select all" checkbox
        if (!$(this).prop('checked')) {
            $('#selectAll').prop('checked', false);
        }
        
        // If all checkboxes are checked, check the "select all" checkbox
        const totalCheckboxes = $('.app-checkbox').length;
        const checkedCheckboxes = $('.app-checkbox:checked').length;
        if (totalCheckboxes === checkedCheckboxes) {
            $('#selectAll').prop('checked', true);
        }
    });
    
    // Update batch acknowledge button state
    function updateBatchButton() {
        const selectedCount = $('.app-checkbox:checked').length;
        $('#batchAcknowledgeBtn').prop('disabled', selectedCount === 0)
            .text(selectedCount > 0 ? `Acknowledge Selected (${selectedCount})` : 'Acknowledge Selected');
    }
    
    // Handle view button click
    $(document).on('click', '.view-btn', function() {
        const id = $(this).data('id');
        
        // Clear previous content
        $('#viewApplicationModalBody').html('<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Loading application details...</p></div>');
        
        // Set the application ID for the acknowledge button in the modal
        $('#modalAcknowledgeBtn').data('id', id);
        
        // Show the modal
        $('#viewApplicationModal').modal('show');
        
        // Fetch application details
        $.ajax({
            url: `<?= base_url('incoming_applicants/view') ?>/${id}`,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#viewApplicationModalBody').html(response.html);
                } else {
                    $('#viewApplicationModalBody').html(`<div class="alert alert-danger">${response.message}</div>`);
                }
            },
            error: function() {
                $('#viewApplicationModalBody').html('<div class="alert alert-danger">Failed to load application details</div>');
            }
        });
    });
    
    // Handle acknowledge button click (individual)
    $(document).on('click', '.acknowledge-btn', function() {
        const id = $(this).data('id');
        acknowledgeApplication(id);
    });
    
    // Handle acknowledge button click in modal
    $(document).on('click', '#modalAcknowledgeBtn', function() {
        const id = $(this).data('id');
        acknowledgeApplication(id);
        $('#viewApplicationModal').modal('hide');
    });
    
    // Handle batch acknowledge button click
    $('#batchAcknowledgeBtn').click(function() {
        const selectedIds = [];
        $('.app-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });
        
        // Confirm batch acknowledge
        if (confirm(`Are you sure you want to acknowledge ${selectedIds.length} application(s)?`)) {
            batchAcknowledgeApplications(selectedIds);
        }
    });
    
    // Function to acknowledge a single application
    function acknowledgeApplication(id) {
        // Get the current CSRF token from the hidden field
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';
        
        // Create data object
        const data = {};
        data[csrfName] = csrfToken;
        
        $.ajax({
            url: `<?= base_url('incoming_applicants/acknowledge') ?>/${id}`,
            type: 'POST',
            data: data,
            dataType: 'json',
            beforeSend: function() {
                // Disable buttons to prevent double submission
                $(`.acknowledge-btn[data-id="${id}"]`).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.success(response.message);
                    
                    // Update CSRF hash with the new one from the response
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                    
                    // Remove the row from the table
                    table.row($(`.app-checkbox[value="${id}"]`).closest('tr')).remove().draw();
                    
                    // Update UI as needed
                    updateBatchButton();
                } else {
                    // Show error message
                    toastr.error(response.message);
                    
                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                    
                    // Re-enable the button
                    $(`.acknowledge-btn[data-id="${id}"]`).prop('disabled', false).html('<i class="fas fa-check-circle"></i> Acknowledge');
                }
            },
            error: function(xhr) {
                // Show error message
                toastr.error('An error occurred while processing your request');
                
                // Try to parse response to get CSRF hash if available
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } catch (e) {
                    console.error('Could not parse response:', e);
                }
                
                // Re-enable the button
                $(`.acknowledge-btn[data-id="${id}"]`).prop('disabled', false).html('<i class="fas fa-check-circle"></i> Acknowledge');
            }
        });
    }
    
    // Function to batch acknowledge applications
    function batchAcknowledgeApplications(ids) {
        // Get the current CSRF token from the hidden field
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';
        
        // Create data object
        const data = {
            ids: ids
        };
        data[csrfName] = csrfToken;
        
        $.ajax({
            url: '<?= base_url('incoming_applicants/batch_acknowledge') ?>',
            type: 'POST',
            data: data,
            dataType: 'json',
            beforeSend: function() {
                // Disable button to prevent double submission
                $('#batchAcknowledgeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.success(response.message);
                    
                    // Update CSRF hash with the new one from the response
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                    
                    // Remove the acknowledged applications from the table
                    ids.forEach(function(id) {
                        table.row($(`.app-checkbox[value="${id}"]`).closest('tr')).remove().draw();
                    });
                    
                    // Uncheck select all checkbox
                    $('#selectAll').prop('checked', false);
                    
                    // Update UI as needed
                    updateBatchButton();
                } else {
                    // Show error message
                    toastr.error(response.message);
                    
                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                    
                    // Re-enable the button
                    $('#batchAcknowledgeBtn').prop('disabled', false).text(`Acknowledge Selected (${ids.length})`);
                }
            },
            error: function(xhr) {
                // Show error message
                toastr.error('An error occurred while processing your request');
                
                // Try to parse response to get CSRF hash if available
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } catch (e) {
                    console.error('Could not parse response:', e);
                }
                
                // Re-enable the button
                $('#batchAcknowledgeBtn').prop('disabled', false).text(`Acknowledge Selected (${ids.length})`);
            }
        });
    }
});
</script>
<?= $this->endSection() ?> 