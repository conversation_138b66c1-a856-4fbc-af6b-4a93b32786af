<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Description" content="Government Recruitment and Selection System" />
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Font Awesome - Local Installation -->
    <link rel="stylesheet" href="<?= base_url() ?>/public/assets/fontawesome/fontawesome-free-6.4.0-web/css/all.min.css">
    <!-- Clean, elegant Source Sans Pro font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- DataTables Bootstrap 5 -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Toastr CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    <!-- Google Charts Library -->
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <!-- JQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <title><?= isset($title) ? $title . ' - ' : '' ?>GRASS</title>

    <style>
        :root {
            --navy: #002B5B;
            --lime: #BFFF00;
            --dark-green: #4D8C57;
            --accent-red: #FF3366;
            --deep-red: #CC2952;
            --navy-light: #003B7B;
        }

        /* Optimize font loading behavior */
        @font-face {
            font-family: 'Source Sans 3';
            font-display: swap;
        }

        body {
            font-family: 'Source Sans 3', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            letter-spacing: -0.01em;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-weight: 400;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Source Sans 3', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-weight: 600;
            letter-spacing: -0.015em;
            line-height: 1.3;
        }

        .btn, .nav-link {
            font-family: 'Source Sans 3', sans-serif;
            font-weight: 500;
            letter-spacing: -0.01em;
        }

        .navbar {
            background: linear-gradient(135deg, var(--navy), var(--navy-light));
            padding: 1rem 0;
        }

        .navbar-brand img {
            height: 40px;
            width: auto;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
        }

        .nav-link:hover {
            color: var(--lime) !important;
        }

        .nav-link.active {
            color: var(--lime) !important;
            font-weight: 500;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .dropdown-item:hover {
            background-color: rgba(255, 51, 102, 0.1);
            color: var(--accent-red);
        }

        .main-content {
            flex: 1;
            padding: 2rem 0;
        }

        .footer {
            background: linear-gradient(135deg, var(--navy), var(--navy-light));
            color: white;
            padding: 1.5rem 0;
            margin-top: auto;
        }

        /* Card Hover Effect */
        .hover-card {
            transition: all 0.3s;
        }

        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 .5rem 1rem rgba(255, 51, 102, 0.15);
        }

        /* Notification Badge */
        .notification-badge {
            background-color: var(--accent-red);
        }

        /* Custom Button Styles */
        .btn-primary {
            background-color: var(--accent-red);
            border-color: var(--accent-red);
        }

        .btn-primary:hover {
            background-color: var(--deep-red);
            border-color: var(--deep-red);
        }

        .btn-outline-primary {
            color: var(--accent-red);
            border-color: var(--accent-red);
        }

        .btn-outline-primary:hover {
            background-color: var(--accent-red);
            border-color: var(--accent-red);
        }

        /* Enhanced typography for content areas */
        p, li, td, th, input, select, textarea {
            line-height: 1.6;
        }

        /* Refined card styling for a more elegant look */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .card-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background-color: transparent;
            font-weight: 500;
        }

        /* Smooth transitions on interactive elements */
        a, button, .btn {
            transition: all 0.2s ease;
        }

        /* Refined table styling */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.03em;
        }
    </style>
</head>

<body>
    <?php if(session()->get('logged_in')): ?>
    <!-- Admin Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid px-4">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url('dashboard') ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="Logo" class="me-3">
                <span class="h4 mb-0">GRASS</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a href="<?= base_url('dashboard') ?>" class="nav-link <?= ($menu == 'dashboard') ? 'active' : '' ?>">
                            Dashboard
                        </a>
                    </li>

                    <!-- Jobs Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Jobs
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= base_url('jobs/create') ?>">Post New Job</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('jobs') ?>">Manage Jobs</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('jobs/categories') ?>">Job Categories</a></li>
                        </ul>
                    </li>

                    <!-- Applications Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Applications
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= base_url('applications') ?>">All Applications</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('applications/shortlisted') ?>">Shortlisted</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('interviews') ?>">Interviews</a></li>
                        </ul>
                    </li>

                    <!-- Profiling Link -->
                    <li class="nav-item">
                        <a href="<?= base_url('applications_profiling_exercise') ?>" class="nav-link <?= ($menu == 'profiling') ? 'active' : '' ?>">
                            Profiling
                        </a>
                    </li>

                    <!-- Reports Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Reports
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= base_url('reports/recruitment') ?>">Recruitment Analytics</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('reports/applicants') ?>">Applicant Reports</a></li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('settings') ?>" class="nav-link <?= ($menu == 'settings') ? 'active' : '' ?>">
                            Settings
                        </a>
                    </li>
                </ul>

                <div class="d-flex align-items-center gap-4">
                    <!-- Notifications -->
                    <div class="position-relative">
                        <button class="btn btn-link text-white position-relative">
                            <i class="fas fa-bell fs-5"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill notification-badge">
                                3
                            </span>
                        </button>
                    </div>

                    <!-- User Menu -->
                    <div class="dropdown">
                        <button class="btn btn-link text-white dropdown-toggle text-decoration-none" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <?= session()->get('name') ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?= base_url('profile') ?>">Profile</a></li>
                            <li><a class="dropdown-item" href="<?= base_url('settings') ?>">Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="<?= base_url('logout') ?>">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid px-4">
            <?= $this->renderSection('content') ?>
        </div>
    </main>

    <?php else: ?>
    <!-- Public Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="Logo" class="me-3">
                <span class="h4 mb-0">GRASS</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if(session()->get('logged_in') && session()->get('applicant_id')): ?>
                        <?php
                        // Calculate profile completion percentage
                        $profile_completion = 0;
                        if(isset($applicant)) {
                            $required_fields = [
                                'fname', 'lname', 'gender', 'dobirth', 'contact_details',
                                'location_address', 'place_of_origin', 'citizenship',
                                'id_numbers', 'current_employer', 'current_position',
                                'marital_status'
                            ];
                            $filled_fields = 0;
                            foreach($required_fields as $field) {
                                if(!empty($applicant[$field])) {
                                    $filled_fields++;
                                }
                            }
                            $profile_completion = round(($filled_fields / count($required_fields)) * 100);
                        }
                        ?>
                        <li class="nav-item">
                            <a href="<?= base_url('applicant/dashboard') ?>" class="nav-link <?= ($menu == 'dashboard') ? 'active' : '' ?>">
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?= base_url('applicant/profile') ?>" class="nav-link <?= ($menu == 'profile') ? 'active' : '' ?>">
                                Profile
                                <div class="progress" style="height: 5px; width: 100%; margin-top: 2px;">
                                    <div class="progress-bar <?= $profile_completion < 100 ? 'bg-warning' : 'bg-success' ?>"
                                         role="progressbar"
                                         style="width: <?= $profile_completion ?>%;"
                                         aria-valuenow="<?= $profile_completion ?>"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <?php if($profile_completion == 100): ?>
                                <a href="<?= base_url('jobs/openings') ?>" class="nav-link <?= ($menu == 'jobs') ? 'active' : '' ?>">
                                    Job Openings
                                </a>
                            <?php else: ?>
                                <a href="#" class="nav-link disabled" data-bs-toggle="tooltip" data-bs-placement="bottom"
                                   title="Complete your profile (<?= $profile_completion ?>% complete) to view and apply for jobs">
                                    Job Openings
                                    <i class="fas fa-lock-alt small ms-1"></i>
                                </a>
                            <?php endif; ?>
                        </li>
                        <li class="nav-item">
                            <a href="<?= base_url('applicant/applications') ?>" class="nav-link <?= ($menu == 'applications') ? 'active' : '' ?>">
                                My Applications
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-2"></i><?= session()->get('name') ?>
                                <?php if($profile_completion < 100): ?>
                                    <span class="badge bg-warning text-dark ms-1"><?= $profile_completion ?>%</span>
                                <?php endif; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="<?= base_url('applicant/profile') ?>">
                                        <div class="d-flex align-items-center">
                                            <span class="flex-grow-1">Profile</span>
                                            <?php if($profile_completion < 100): ?>
                                                <span class="badge bg-warning text-dark ms-2"><?= $profile_completion ?>%</span>
                                            <?php endif; ?>
                                        </div>
                                    </a>
                                </li>
                                <li><a class="dropdown-item" href="<?= base_url('applicant/settings') ?>">Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="<?= base_url('logout') ?>">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a href="<?= base_url() ?>" class="nav-link <?= ($menu == 'home') ? 'active' : '' ?>">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="<?= base_url('login') ?>" class="nav-link <?= ($menu == 'login') ? 'active' : '' ?>">Login</a>
                        </li>
                        <li class="nav-item">
                            <a href="<?= base_url('about') ?>" class="nav-link <?= ($menu == 'about') ? 'active' : '' ?>">About</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <?php if(session()->get('logged_in') && session()->get('applicant_id') && $profile_completion < 100): ?>
        <div class="alert alert-warning alert-dismissible fade show m-0" role="alert">
            <div class="container d-flex align-items-center">
                <div class="flex-grow-1">
                    <i class="fas fa-info-circle me-2"></i>
                    Your profile is <?= $profile_completion ?>% complete. Complete your profile to increase your chances of getting hired.
                </div>
                <a href="<?= base_url('applicant/profile') ?>" class="btn btn-sm btn-warning ms-3">Complete Profile</a>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?= $this->renderSection('content') ?>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                <div class="mb-3">
                    <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii" height="32">
                </div>
                <p class="small mb-1">&copy; 2024 <a href="https://www.dakoiims.com" class="text-lime text-decoration-none">Dakoii Systems</a></p>
                <p class="small mb-0"><?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></p>
            </div>
        </div>
    </footer>

    <!-- Core JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <!-- Toastr Messages -->
    <script>
        // Configure Toastr options
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        <?php if (session()->getFlashdata('success')): ?>
            toastr.success(<?= json_encode(session()->getFlashdata('success')); ?>);
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            toastr.error(<?= json_encode(session()->getFlashdata('error')); ?>);
        <?php endif; ?>
    </script>

    <!-- Page specific scripts -->
    <?= $this->renderSection('scripts') ?>
</body>
</html>