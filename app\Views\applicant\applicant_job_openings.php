<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3">Job Openings</h1>
            <p class="text-muted">Browse through available job exercises and click to view positions.</p>
        </div>
    </div>

    <?php if (empty($jobData)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                <h5>No Job Openings Available</h5>
                <p class="text-muted">There are currently no published job exercises.</p>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($jobData as $data): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <?= esc($data['organization']['name']) ?>
                            </h5>
                            <?php if (!empty($data['organization']['orglogo'])): ?>
                                <div class="text-center mb-4">
                                    <img src="<?= base_url(str_replace('public/', '', $data['organization']['orglogo'])) ?>" 
                                         alt="Organization Logo" 
                                         class="img-fluid" 
                                         style="max-height: 100px;">
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h6 class="card-subtitle text-muted">
                                    Exercise #<?= esc($data['exercise']['gazzetted_no']) ?>
                                </h6>
                                <span class="badge bg-success">
                                    <?= $data['position_count'] ?> Position<?= $data['position_count'] > 1 ? 's' : '' ?>
                                </span>
                            </div>
                            <p class="card-text">
                                <strong>Advertisement No:</strong> <?= esc($data['exercise']['advertisement_no']) ?><br>
                                <strong>Published:</strong> <?= date('M d, Y', strtotime($data['exercise']['publish_date_from'])) ?><br>
                                <strong>Closes:</strong> <?= date('M d, Y', strtotime($data['exercise']['publish_date_to'])) ?>
                            </p>
                            <div class="text-end">
                                <a href="<?= base_url('applicant/jobs/' . $data['exercise']['id']) ?>" 
                                   class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>View Positions
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Add any necessary JavaScript functionality here
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 